# SECTION 1: Imports & Parameters
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import json
from datetime import datetime
import tensorflow as tf


# Parameters
site_name = "trino_enel"
ground_method = "csf"
confidence_threshold = 0.5  # LOWERED from 0.6 or 0.7
output_dir = Path("../../../../../data/output_runs/pile_detection") / ground_method
output_dir.mkdir(parents=True, exist_ok=True)
print(f"Saving results to: {output_dir}")

point_cloud_file = Path(f"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply")

# Setup
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)
detections_file = output_path / f"c_section_detections_{ground_method}.csv"
print(f"Saving detections to: {detections_file}")


# === 2. LOAD POINT CLOUD ===
print("Loading point cloud...")
if not point_cloud_file.exists():
    point_cloud_file = Path(f"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply")

pcd = o3d.io.read_point_cloud(str(point_cloud_file))
points = np.asarray(pcd.points)

print(f"Loaded {points.shape[0]:,} points from {point_cloud_file}")


# === 3. ENHANCED CONFIGURATION ===

import pandas as pd
from pathlib import Path
import numpy as np
from sklearn.neighbors import NearestNeighbors
from sklearn.model_selection import train_test_split
import json

class CSectionPileConfig:
    """Enhanced configuration for C-section pile detection with IFC ground truth"""
    
    def __init__(self):
        # === IFC Ground Truth Data ===
        self.ifc_pile_csv = "data/processed/trino_enel/advanced_ifc_metadata/advanced_tracker_piles.csv"
        self.expected_pile_count = 14460  # Ground truth from IFC
        
        # === C-section Physical Dimensions ===
        # Based on TRJHT56PDP-BF model specifications
        self.flange_width_range = (0.05, 0.3)      # meters
        self.web_height_range = (0.1, 0.8)         # meters  
        self.thickness_range = (0.005, 0.03)       # meters
        self.opening_width_range = (0.08, 0.25)    # meters
        self.pile_height_range = (1.0, 5.0)        # meters above ground
        
        # === Patch Extraction Parameters ===
        self.patch_radius = 1.5                    # meters - spatial radius for patch extraction
        self.num_points_per_patch = 512             # consistent point count per patch
        self.min_points_per_patch = 128             # minimum points to consider valid patch
        self.overlap_ratio = 0.3                   # overlap between patches for augmentation
        
        # === Training Data Parameters ===
        self.positive_sample_ratio = 0.7           # ratio of IFC piles to use as positives
        self.negative_sample_multiplier = 2.0      # negatives per positive sample
        self.validation_split = 0.2                # validation data split
        self.test_split = 0.1                      # test data split
        
        # === Model Architecture Parameters ===
        self.num_classes = 2                       # pile vs non-pile
        self.dropout_rate = 0.3                    # dropout for regularization
        self.learning_rate = 0.001                 # initial learning rate
        self.batch_size = 16                       # training batch size
        self.epochs = 50                           # training epochs
        
        # === Geometric Detection (Legacy) ===
        self.min_height = 0.8                      # minimum Z elevation from ground
        self.expected_width = 0.15                 # expected pile width
        self.expected_depth = 0.08                 # expected pile depth  
        self.width_tolerance = 0.1                 # allowed variation in width
        self.min_points_per_cluster = 50           # minimum points in cluster
        self.confidence_threshold = 0.7            # minimum score for detection
        self.dbscan_eps = 0.25                     # radius for clustering
        self.max_cluster_size = 500                # ignore overly large clusters
        
        # === Coordinate System ===
        self.utm_zone = "32N"                      # UTM Zone 32N (EPSG:32632)
        self.coordinate_bounds = {
            'x_min': 435267, 'x_max': 436720,      # UTM X bounds
            'y_min': 5010901, 'y_max': 5012462,    # UTM Y bounds  
            'z_min': 157.1, 'z_max': 161.7         # elevation bounds
        }
        
        # === Output Configuration ===
        self.save_patches = True                    # save extracted patches for analysis
        self.save_model = True                      # save trained model
        self.mlflow_tracking = True                 # enable MLflow experiment tracking
        self.visualization = True                   # enable result visualization

# Initialize configuration
config = CSectionPileConfig()
print(f"Configuration initialized:")
print(f"  Expected piles: {config.expected_pile_count}")
print(f"  IFC data source: {config.ifc_pile_csv}")
print(f"  Patch radius: {config.patch_radius}m")
print(f"  Points per patch: {config.num_points_per_patch}")
print(f"  Training epochs: {config.epochs}")

# === 4. LOAD IFC GROUND TRUTH DATA ===

def load_ifc_ground_truth(config):
    """Load IFC pile coordinates as ground truth data"""
    try:
        # Load IFC pile data
        ifc_piles = pd.read_csv(config.ifc_pile_csv)
        print(f"Loaded {len(ifc_piles)} IFC pile records")
        
        # Extract coordinates
        pile_coords = ifc_piles[['X', 'Y', 'Z']].values
        pile_ids = ifc_piles['Tag'].values
        pile_names = ifc_piles['Name'].values
        
        # Validate coordinate bounds
        bounds = config.coordinate_bounds
        valid_mask = (
            (pile_coords[:, 0] >= bounds['x_min']) & (pile_coords[:, 0] <= bounds['x_max']) &
            (pile_coords[:, 1] >= bounds['y_min']) & (pile_coords[:, 1] <= bounds['y_max']) &
            (pile_coords[:, 2] >= bounds['z_min']) & (pile_coords[:, 2] <= bounds['z_max'])
        )
        
        valid_coords = pile_coords[valid_mask]
        valid_ids = pile_ids[valid_mask]
        valid_names = pile_names[valid_mask]
        
        print(f"Valid piles within bounds: {len(valid_coords)}")
        print(f"Coordinate ranges:")
        print(f"  X: {valid_coords[:, 0].min():.1f} - {valid_coords[:, 0].max():.1f}")
        print(f"  Y: {valid_coords[:, 1].min():.1f} - {valid_coords[:, 1].max():.1f}")
        print(f"  Z: {valid_coords[:, 2].min():.1f} - {valid_coords[:, 2].max():.1f}")
        
        return {
            'coordinates': valid_coords,
            'ids': valid_ids,
            'names': valid_names,
            'count': len(valid_coords)
        }
        
    except Exception as e:
        print(f"Error loading IFC ground truth: {e}")
        return None

# Load ground truth data
ground_truth = load_ifc_ground_truth(config)
if ground_truth:
    print(f"\nGround truth loaded successfully!")
    print(f"Total valid piles: {ground_truth['count']}")
else:
    print("Failed to load ground truth data")

def filter_elevated_points(pts, min_height):
    # Step 1: Filter points that are above ground level
    zmin = np.min(pts[:, 2])
    mask = (pts[:, 2] - zmin) >= min_height
    return pts[mask]

# Define constants for geometric detection (legacy compatibility)
MIN_HEIGHT = config.min_height
EXPECTED_WIDTH = config.expected_width
EXPECTED_DEPTH = config.expected_depth
WIDTH_TOLERANCE = config.width_tolerance
MIN_POINTS_PER_CLUSTER = config.min_points_per_cluster
CONFIDENCE_THRESHOLD = config.confidence_threshold
DBSCAN_EPS = config.dbscan_eps
MAX_CLUSTER_SIZE = config.max_cluster_size

def cluster_vertical_structures(elevated_pts):
    # Step 2: Cluster nearby elevated points using DBSCAN
    labels = DBSCAN(eps=DBSCAN_EPS, min_samples=MIN_POINTS_PER_CLUSTER).fit_predict(elevated_pts[:, :2])

    print(f"Found {len(set(labels)) - 1} vertical structure candidates")
    clusters = [elevated_pts[labels == i] for i in set(labels) if i != -1]
    print(f"  Cluster sizes: {[len(c) for c in clusters]}")

    return clusters

def calculate_distribution_score(pts):
    # Evaluate how linearly or elongated the points are using PCA
    if len(pts) < 20:
        return 0.0
    
    centered = pts[:, :2] - np.mean(pts[:, :2], axis=0)
    pca = PCA(n_components=2).fit(centered)
    transformed = pca.transform(centered)
    stds = np.std(transformed, axis=0)
    
    ratio = max(stds) / (min(stds) + 1e-6)
    print(f"  Distribution score: {ratio:.2f}")
    
    # Higher score if the shape is clearly elongated
    return 0.8 if ratio > 2.0 else 0.6 if ratio > 1.5 else 0.3

def analyze_cluster(cluster):
    # Step 3: Analyze a given cluster’s shape and point distribution
    x_span = np.ptp(cluster[:, 0])
    y_span = np.ptp(cluster[:, 1])
    z_span = np.ptp(cluster[:, 2])

    # Score based on how tall the structure is
    h_score = min(z_span / MIN_HEIGHT, 1.0)
    max_h = max(x_span, y_span)
    min_h = min(x_span, y_span)
    
    # Check if the widest horizontal side matches expected width
    width_score = 0.7 if (EXPECTED_WIDTH - WIDTH_TOLERANCE <= max_h <= EXPECTED_WIDTH + WIDTH_TOLERANCE) else 0.0
    aspect = max_h / (min_h + 1e-6)
    aspect_score = 0.8 if 1.5 <= aspect <= 4.0 else 0.0
    dist_score = calculate_distribution_score(cluster)

    # Score based on aspect ratio (width-to-depth ratio)
    confidence = h_score * 0.3 + width_score * 0.3 + aspect_score * 0.2 + dist_score * 0.2
    print(f"Cluster analysis: h={h_score:.2f}, w={width_score:.2f}, a={aspect_score:.2f}, d={dist_score:.2f}")

    return confidence, z_span, max_h, min_h, len(cluster), dist_score

def compute_center(cluster):
    # Compute the center position of the structure
    zmin = np.min(cluster[:, 2])
    base_points = cluster[cluster[:, 2] <= zmin + 0.2 * np.ptp(cluster[:, 2])]
    print(f"Base points: {len(base_points)}")
    return base_points.mean(axis=0) if len(base_points) > 0 else cluster.mean(axis=0)

def detect_piles_from_pointcloud(pts):
    """End-to-end detection pipeline."""
    print("Running geometric detection pipeline...")

    elevated = filter_elevated_points(pts, MIN_HEIGHT)
    print(f"Points above {MIN_HEIGHT}m: {len(elevated):,}")

    if len(elevated) < MIN_POINTS_PER_CLUSTER:
        return []

    clusters = cluster_vertical_structures(elevated)
    print(f"Found {len(clusters)} structure candidates")

    results = []
    for i, cluster in enumerate(clusters):
        if len(cluster) > MAX_CLUSTER_SIZE:
            continue  # Optional: skip noisy blobs

        conf, h, w, d, n, ds = analyze_cluster(cluster)
        if conf >= CONFIDENCE_THRESHOLD:
            center = compute_center(cluster)
            results.append({
                "x": center[0], "y": center[1], "z": center[2], "confidence": conf,
                "width": w, "height": h, "depth": d,
                "point_count": n, "distribution_score": ds,
                "detection_method": "geometric"
            })
        else:
            print(f"Rejected cluster {i}: conf={conf:.2f}, h={h:.2f}, w={w:.2f}, pts={n}")
    
    print(f"Detected {len(results)} C-section piles")
    return results


from scipy.spatial import cKDTree

def filter_detections_against_ifc(detections, ifc_df, radius=1.5):
    """Retain only detections that are within a given distance to known IFC piles."""
    ifc_coords = ifc_df[["X", "Y"]].values
    tree = cKDTree(ifc_coords)

    filtered = []
    for det in detections:
        dist, _ = tree.query([det["x"], det["y"]], distance_upper_bound=radius)
        if dist < radius:
            filtered.append(det)

    print(f"{len(filtered)} detections matched IFC locations (within {radius}m)")
    return filtered

# === OPTIONAL: Run Geometric Detection for Comparison ===
# Note: This is now optional since we use IFC ground truth for training

run_geometric_detection = True  # Set to False to skip geometric detection

if run_geometric_detection:
    print("Running geometric detection for comparison...")
    geo_detections = detect_piles_from_pointcloud(points)
    print(f"Geometric detection found: {len(geo_detections)} C-section piles")
    print(f"IFC ground truth has: {ground_truth['count'] if ground_truth else 'N/A'} piles")
    
    if ground_truth:
        print(f"Geometric vs IFC ratio: {len(geo_detections) / ground_truth['count']:.3f}")
else:
    print("Skipping geometric detection - using IFC ground truth only")
    geo_detections = []

import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2)
for d in geo_detections:
    plt.scatter(d['x'], d['y'], c='orange', s=50, edgecolor='black')
plt.axis('equal')
plt.title(f"C-section Pile Detections: {len(geo_detections)}")
plt.xlabel("X"), plt.ylabel("Y")
plt.grid(True)
plt.show()


from tabulate import tabulate
print("Geo detections:", tabulate(geo_detections))


# SECTION 4: PointNet++-style model for binary classification

class PointNetPPClassifier(tf.keras.Model):
    def __init__(self, num_classes=2):
        super().__init__()
        self.net = tf.keras.Sequential([
            tf.keras.layers.Conv1D(64, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv1D(128, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv1D(256, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.GlobalMaxPooling1D(),
            tf.keras.layers.Dense(256, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(128, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(num_classes, activation='softmax')
        ])

    def call(self, x, training=False):
        return self.net(x, training=training)


def extract_patch_spatial(center, points, config):
    """Extract patch using spatial radius instead of nearest neighbors"""
    try:
        # Calculate distances from center
        distances = np.linalg.norm(points[:, :3] - center[:3], axis=1)
        
        # Filter points within radius
        within_radius = distances <= config.patch_radius
        patch_points = points[within_radius]
        
        if len(patch_points) < config.min_points_per_patch:
            return None
        
        # Sample or pad to consistent size
        if len(patch_points) > config.num_points_per_patch:
            # Randomly sample points
            indices = np.random.choice(len(patch_points), config.num_points_per_patch, replace=False)
            patch_points = patch_points[indices]
        elif len(patch_points) < config.num_points_per_patch:
            # Pad with repeated points
            needed = config.num_points_per_patch - len(patch_points)
            indices = np.random.choice(len(patch_points), needed, replace=True)
            padding = patch_points[indices]
            patch_points = np.vstack([patch_points, padding])
        
        # Center the patch (preserve scale information)
        patch_center = patch_points.mean(axis=0)
        patch_points_centered = patch_points - patch_center
        
        # Optional: normalize by patch radius (not global max)
        max_dist = np.max(np.linalg.norm(patch_points_centered[:, :3], axis=1))
        if max_dist > 0:
            patch_points_centered[:, :3] /= max_dist
        
        return patch_points_centered
        
    except Exception as e:
        print(f"[WARN] Failed to extract patch at center {center}: {e}")
        return None

# Legacy function for compatibility
def extract_patch(center, n_points=128, fallback_n=64):
    """Legacy patch extraction - use extract_patch_spatial instead"""
    return extract_patch_spatial(center, points, config)

# === 5. GENERATE TRAINING DATA FROM IFC GROUND TRUTH ===

def generate_training_data(points, ground_truth, config):
    """Generate training patches using IFC ground truth instead of geometric detections"""
    
    if ground_truth is None:
        print("No ground truth data available")
        return None, None
    
    positive_patches = []
    negative_patches = []
    failed_positive = 0
    failed_negative = 0
    
    # Extract positive patches from IFC pile locations
    pile_coords = ground_truth['coordinates']
    num_positives = int(len(pile_coords) * config.positive_sample_ratio)
    
    print(f"Extracting positive patches from {num_positives} IFC pile locations...")
    
    # Randomly sample pile locations for training
    selected_piles = np.random.choice(len(pile_coords), num_positives, replace=False)
    
    for idx in selected_piles:
        pile_center = pile_coords[idx]
        patch = extract_patch_spatial(pile_center, points, config)
        if patch is not None:
            positive_patches.append(patch)
        else:
            failed_positive += 1
    
    print(f"Positive patches extracted: {len(positive_patches)}, Failed: {failed_positive}")
    
    # Generate negative patches from areas NOT near piles
    num_negatives = int(len(positive_patches) * config.negative_sample_multiplier)
    
    print(f"Generating {num_negatives} negative patches...")
    
    # Create exclusion zones around known piles
    exclusion_radius = config.patch_radius * 2  # Avoid areas too close to piles
    
    attempts = 0
    max_attempts = num_negatives * 10  # Prevent infinite loops
    
    while len(negative_patches) < num_negatives and attempts < max_attempts:
        # Random point from point cloud
        random_idx = np.random.randint(0, len(points))
        candidate_center = points[random_idx]
        
        # Check if too close to any pile
        distances_to_piles = np.linalg.norm(pile_coords - candidate_center[:3], axis=1)
        min_distance = np.min(distances_to_piles)
        
        if min_distance > exclusion_radius:
            patch = extract_patch_spatial(candidate_center, points, config)
            if patch is not None:
                negative_patches.append(patch)
            else:
                failed_negative += 1
        
        attempts += 1
    
    print(f"Negative patches generated: {len(negative_patches)}, Failed: {failed_negative}")
    
    if len(positive_patches) == 0 or len(negative_patches) == 0:
        print("Insufficient training data generated")
        return None, None
    
    # Combine patches and labels
    all_patches = positive_patches + negative_patches
    all_labels = [1] * len(positive_patches) + [0] * len(negative_patches)
    
    # Convert to numpy arrays
    patches = np.array(all_patches)
    labels = np.array(all_labels)
    
    print(f"\nTraining data summary:")
    print(f"  Total samples: {len(patches)}")
    print(f"  Patch shape: {patches.shape}")
    print(f"  Positive samples: {len(positive_patches)}")
    print(f"  Negative samples: {len(negative_patches)}")
    print(f"  Class balance: {len(positive_patches)/(len(positive_patches)+len(negative_patches)):.2f}")
    
    return patches, labels

# Generate training data
patches, labels = generate_training_data(points, ground_truth, config)

if patches is not None:
    print("\nTraining data generated successfully!")
else:
    print("Failed to generate training data")


# Generate random negative patches
negative_patches = []
neg_centers = points[np.random.choice(len(points), size=1000, replace=False)]
for c in neg_centers:
    patch = extract_patch(c)
    if patch is not None:
        negative_patches.append(patch)


import random
from sklearn.utils import shuffle

# Balance: sample negatives to match number of positives
num_pos = len(positive_patches)
num_neg = len(negative_patches)

if num_pos == 0:
    print("No positive patches found. Skipping training.")
    patches = np.array([])
    labels = np.array([])
else:
    if num_neg > num_pos:
        negative_patches = random.sample(negative_patches, num_pos)
        print(f"Balanced dataset: {num_pos} positives, {num_pos} negatives")
    else:
        print(f"Imbalanced: {num_pos} positives, {num_neg} negatives")

    # Combine and shuffle
    patches = np.array(positive_patches + negative_patches)
    labels = np.array([1] * len(positive_patches) + [0] * len(negative_patches))
    patches, labels = shuffle(patches, labels, random_state=42)

print(f"\nTraining patches: {patches.shape}")
print(f"Positive: {len(positive_patches)}, ❌ Negative: {len(negative_patches)}")


# SECTION 6: Train PointNet++ model + Save History

import matplotlib.pyplot as plt
from datetime import datetime

# === 6. TRAIN MODEL WITH CORRECTED DATA ===

if patches is None:
    print("No training data available. Skipping training.")
    final_detections = []
else:
    # Split data for training and validation
    from sklearn.model_selection import train_test_split
    
    X_train, X_val, y_train, y_val = train_test_split(
        patches, labels, 
        test_size=config.validation_split, 
        stratify=labels, 
        random_state=42
    )
    
    print(f"Training set: {len(X_train)} samples")
    print(f"Validation set: {len(X_val)} samples")
    
    # Initialize and compile model
    model = PointNetPPClassifier(num_classes=config.num_classes)
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=config.learning_rate),
        loss='sparse_categorical_crossentropy', 
        metrics=['accuracy']
    )
    
    # Train model
    print("\nStarting training...")
    history = model.fit(
        X_train,
        y_train,
        epochs=config.epochs,
        batch_size=config.batch_size,
        validation_data=(X_val, y_val),
        verbose=1
    )

    # Plot training history
    plt.figure(figsize=(10, 4))
    plt.plot(history.history['accuracy'], label='Train Acc')
    plt.plot(history.history['val_accuracy'], label='Val Acc')
    plt.plot(history.history['loss'], label='Train Loss')
    plt.plot(history.history['val_loss'], label='Val Loss')
    plt.title('PointNet++ Training History')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy / Loss')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig(output_dir / f'pointnetpp_training_history_{ground_method}.png', dpi=300)
    plt.show()

    # Optional: Save model
    if config.save_model:
        model.save(output_dir / f'pointnetpp_model_{ground_method}.h5')
        print("Model saved.")

    # === 7. VALIDATE AGAINST IFC GROUND TRUTH ===
    
    def validate_against_ifc_ground_truth(model, points, ground_truth, config):
        """Validate model predictions against IFC pile locations"""
        
        if ground_truth is None or model is None:
            print("Cannot validate: missing ground truth or model")
            return None
        
        pile_coords = ground_truth['coordinates']
        pile_ids = ground_truth['ids']
        
        print(f"\nValidating against {len(pile_coords)} IFC pile locations...")
        
        # Test model on IFC pile locations
        true_positives = 0
        false_negatives = 0
        validation_results = []
        
        for i, pile_center in enumerate(pile_coords[:100]):  # Test subset for speed
            patch = extract_patch_spatial(pile_center, points, config)
            if patch is not None:
                # Predict with model
                patch_batch = np.expand_dims(patch, axis=0)
                prediction = model.predict(patch_batch, verbose=0)
                predicted_class = np.argmax(prediction[0])
                confidence = np.max(prediction[0])
                
                validation_results.append({
                    'pile_id': pile_ids[i],
                    'coordinates': pile_center,
                    'predicted_class': predicted_class,
                    'confidence': confidence,
                    'is_pile_detected': predicted_class == 1
                })
                
                if predicted_class == 1:  # Model detected pile
                    true_positives += 1
                else:
                    false_negatives += 1
            else:
                false_negatives += 1
        
        # Calculate metrics
        total_tested = true_positives + false_negatives
        recall = true_positives / total_tested if total_tested > 0 else 0
        
        print(f"\nValidation Results:")
        print(f"  Tested pile locations: {total_tested}")
        print(f"  True positives (correctly detected): {true_positives}")
        print(f"  False negatives (missed piles): {false_negatives}")
        print(f"  Recall (detection rate): {recall:.3f}")
        
        # Show confidence distribution
        if validation_results:
            confidences = [r['confidence'] for r in validation_results if r['is_pile_detected']]
            if confidences:
                print(f"  Average confidence for detected piles: {np.mean(confidences):.3f}")
                print(f"  Confidence range: {np.min(confidences):.3f} - {np.max(confidences):.3f}")
        
        return {
            'true_positives': true_positives,
            'false_negatives': false_negatives,
            'recall': recall,
            'total_tested': total_tested,
            'results': validation_results
        }
    
    # Validate model
    validation_metrics = validate_against_ifc_ground_truth(model, points, ground_truth, config)
    print("\nValidation against IFC ground truth completed!")


    # === 8. INFERENCE AND FINAL DETECTIONS ===
    
    # Test model on training patches for analysis
    if patches is not None and len(patches) > 0:
        probs = model.predict(patches)
        confidences = probs[:, 1]  # class 1 = pile
        preds = confidences >= confidence_threshold
        
        print(f"\nTraining set inference:")
        print(f"  Total patches: {len(patches)}")
        print(f"  Predicted as piles: {np.sum(preds)}")
        print(f"  Average confidence: {np.mean(confidences):.3f}")
    
    # Generate final detections based on approach used
    final_detections = []
    
    if run_geometric_detection and len(geo_detections) > 0:
        # Traditional approach: filter geometric detections with model
        print(f"\nFiltering {len(geo_detections)} geometric detections with trained model...")
        
        for i, det in enumerate(geo_detections):
            patch = extract_patch_spatial([det['x'], det['y'], det['z']], points, config)
            if patch is not None:
                patch_batch = np.expand_dims(patch, axis=0)
                prob = model.predict(patch_batch, verbose=0)
                confidence = prob[0][1]  # pile class probability
                
                if confidence >= confidence_threshold:
                    det_copy = det.copy()
                    det_copy['model_confidence'] = float(confidence)
                    det_copy['source'] = 'geometric_filtered'
                    final_detections.append(det_copy)
        
        print(f"Final detections after model filtering: {len(final_detections)} / {len(geo_detections)}")
    
    else:
        # New approach: use model predictions on IFC validation results
        print(f"\nUsing model validation results as final detections...")
        
        if 'validation_metrics' in locals() and validation_metrics:
            for result in validation_metrics['results']:
                if result['is_pile_detected'] and result['confidence'] >= confidence_threshold:
                    coords = result['coordinates']
                    final_detections.append({
                        'x': coords[0],
                        'y': coords[1], 
                        'z': coords[2],
                        'confidence': result['confidence'],
                        'pile_id': result['pile_id'],
                        'source': 'ifc_validated',
                        'model_confidence': result['confidence']
                    })
            
            print(f"Final detections from IFC validation: {len(final_detections)}")
        else:
            print("No validation results available for final detections")


import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))

# Background point cloud
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2, label='All Points')

# Final detections after PointNet++
for d in final_detections:
    plt.scatter(d['x'], d['y'], c='limegreen', s=50, edgecolor='black', label='Final Detection')

plt.axis('equal')
plt.title(f"Final C-section Detections After PointNet++: {len(final_detections)}")
plt.xlabel("X")
plt.ylabel("Y")
plt.grid(True)

# Prevent duplicate legend entries
handles, labels = plt.gca().get_legend_handles_labels()
by_label = dict(zip(labels, handles))
plt.legend(by_label.values(), by_label.keys(), loc='upper right')

plt.show()


probs = model.predict(patches, batch_size=64)
print("Min:", np.min(probs[:, 1]), "Max:", np.max(probs[:, 1]), "Mean:", np.mean(probs[:, 1]))


import seaborn as sns
plt.figure(figsize=(8, 4))
sns.histplot(confidences, bins=50, kde=True)
plt.axvline(confidence_threshold, color='red', linestyle='--', label=f"Threshold = {confidence_threshold}")
plt.title("Model Predicted Confidence Scores (Class = 1)")
plt.xlabel("Confidence")
plt.ylabel("Count")
plt.legend()
plt.grid(True)
plt.show()

# SECTION 8: Save final detections
df = pd.DataFrame(final_detections)
df.to_csv(output_dir / f"c_section_detections_final.csv", index=False)

# === ENHANCED METRICS WITH IFC VALIDATION ===
metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'ground_method': ground_method,
    'confidence_threshold': confidence_threshold,
    'input_points': len(points),
    'geometric_detections': len(geo_detections) if 'geo_detections' in locals() else 0,
    'final_detections': len(final_detections) if 'final_detections' in locals() else 0,
    'ifc_ground_truth_piles': ground_truth['count'] if ground_truth else 0,
    'training_samples': len(patches) if patches is not None else 0,
    'model_epochs': config.epochs if 'config' in locals() else 0,
    'patch_radius': config.patch_radius if 'config' in locals() else 0,
    'approach': 'ifc_ground_truth_enhanced'
}

# Add validation metrics if available
if 'validation_metrics' in locals() and validation_metrics:
    metrics.update({
        'validation_recall': validation_metrics['recall'],
        'validation_true_positives': validation_metrics['true_positives'],
        'validation_false_negatives': validation_metrics['false_negatives'],
        'validation_tested': validation_metrics['total_tested']
    })

with open(output_dir / f"metrics.json", 'w') as f:
    json.dump(metrics, f, indent=2)

print("Results saved.")


# SECTION 9: Visualization (Top view)

plt.figure(figsize=(10, 6))
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.3)
for d in final_detections:
    plt.scatter(d['x'], d['y'], c='orange', s=100, edgecolor='black')
plt.title(f"C-section Pile Detections: {len(final_detections)}")
plt.xlabel("X")
plt.ylabel("Y")
plt.axis("equal")
plt.grid(True)
plt.show()


